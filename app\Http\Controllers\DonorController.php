<?php

namespace App\Http\Controllers;

use App\Services\DonorService;
use App\Models\DonationToken;
use Illuminate\Http\Request;

class DonorController extends Controller
{


    protected $donorService;

    public function __construct(DonorService $donorService)
    {
        $this->donorService = $donorService;
    }

    public function detailView(Request $request)
    {
        $data = $this->donorService->getDetailViewData($request);

        // Generate a donation token
        $donationToken = DonationToken::generateToken(
            $data['amount'],
            $data['user']->slug
        );

        // Redirect to the GET route with the token
        return redirect()->route('donor.payment', ['token' => $donationToken->token]);
    }

    public function paymentView($token)
    {
        // Find the valid token
        $donationToken = DonationToken::findValidToken($token);

        if (!$donationToken) {
            return redirect()->back()->with('error', 'Invalid or expired donation link. Please try again.');
        }

        // Get the user data using the relationship
        $user = \App\Models\User::where('slug', $donationToken->user_slug)->first();
        $amount = $donationToken->amount;

        return view('donor.detail', compact('user', 'amount'));
    }
}
